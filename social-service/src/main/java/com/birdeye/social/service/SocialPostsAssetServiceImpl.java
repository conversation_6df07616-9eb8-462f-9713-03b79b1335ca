package com.birdeye.social.service;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.dao.SocialPostsAssetsRepository;
import com.birdeye.social.dto.BusinessLiteDTO;
import com.birdeye.social.entities.SocialPostsAssets;
import com.birdeye.social.model.MediaBucket;
import com.birdeye.social.model.MediaData;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.*;
import java.util.stream.Collectors;

import static com.birdeye.social.constant.Constants.*;

@Service
public class SocialPostsAssetServiceImpl implements ISocialPostsAssetService {

    @Autowired
    private SocialPostsAssetsRepository socialPostsAssetsRepository;

    @Autowired
    private IBusinessCoreService businessCoreService;

    private static final Logger LOGGER = LoggerFactory.getLogger(SocialPostsAssetServiceImpl.class);

    @Override
    public List<String> findImageUrlsByIds(List<Integer> ids, String businessNum) {
        List<SocialPostsAssetsRepository.SocialPostAssetsBasicDetails> basicDetails =  socialPostsAssetsRepository.findByIdIn(ids);
        if(CollectionUtils.isNotEmpty(ids) && CollectionUtils.isNotEmpty(basicDetails)) {
            basicDetails.sort(Comparator.comparingInt(dto -> ids.indexOf(dto.getId())));
        }
        List<String> imageUrls = new ArrayList<>();
        basicDetails.forEach(basicDetail -> {
            imageUrls.add(getCompleteMediaUrlFromParam(basicDetail.getImageUrl(),basicDetail.getBucketId(),businessNum));
        });
        return imageUrls;
    }

    @Override
    public Map<String, String> getIdVsImageUrlsByIds(List<Integer> ids, String businessNum) {
        List<SocialPostsAssetsRepository.SocialPostAssetsBasicDetails> basicDetails =  socialPostsAssetsRepository.findByIdIn(ids);
        if(CollectionUtils.isNotEmpty(ids) && CollectionUtils.isNotEmpty(basicDetails)) {
            basicDetails.sort(Comparator.comparingInt(dto -> ids.indexOf(dto.getId())));
        }
        Map<String, String> imageUrls = new HashMap<>();
        basicDetails.forEach(basicDetail -> {
            imageUrls.put(String.valueOf(basicDetail.getId()), getCompleteMediaUrlFromParam(basicDetail.getImageUrl(),basicDetail.getBucketId(),businessNum));
        });
        return imageUrls;
    }

    @Override
    public List<String> findVideoUrlsByIds(List<Integer> ids, String businessNum) {
        List<SocialPostsAssetsRepository.SocialPostAssetsBasicDetails> basicDetails =  socialPostsAssetsRepository.findByIdIn(ids);
        if(CollectionUtils.isNotEmpty(ids) && CollectionUtils.isNotEmpty(basicDetails)) {
            basicDetails.sort(Comparator.comparingInt(dto -> ids.indexOf(dto.getId())));
        }
        List<String> videoUrls = new ArrayList<>();
        basicDetails.forEach(basicDetail -> {
            videoUrls.add(getCompleteMediaUrlFromParam(basicDetail.getVideoUrl(),basicDetail.getBucketId(),businessNum));
        });
        return videoUrls;
    }

    @Override
    public SocialPostsAssets findById(Integer id) {
        return socialPostsAssetsRepository.findById(id);
    }

    @Override
    public String getCompleteCdnUrlFromBaseUrl(String url, String enterpriseNum) {
        return getCompleteMediaUrlFromParam(url,null,enterpriseNum);
    }

    @Override
    public List<SocialPostsAssets> findAllByImageUrl(List<String> imageUrls) {
        return socialPostsAssetsRepository.findAllByImageUrl(imageUrls);
    }

    @Override
    public List<SocialPostsAssets> findAllByImageUrlAndBucketId(List<String> imageUrls, String bucketId) {
        return socialPostsAssetsRepository.findAllByImageUrlAndBucketId(imageUrls, bucketId);
    }

    @Override
    @Transactional
    public void save(SocialPostsAssets asset) {
        socialPostsAssetsRepository.save(asset);
    }

    @Override
    public void save(List<SocialPostsAssets> assets) {
        socialPostsAssetsRepository.save(assets);
    }

    @Override
    public String getCompleteImageUrlFromPostAsset(SocialPostsAssets post, String businessNum) {
        return getCompleteMediaUrlFromParam(post.getImageUrl(),post.getBucketId(),businessNum);
    }

    private String getCompleteMediaUrlFromParam(String url, String bucketId, String businessNum) {
        String cdnUrl = getBaseCdnUrl();
        if(url.indexOf(SOCIAL_TEMPLATE)>=0){
            return cdnUrl + "/" + url;
        } else {
            return cdnUrl + "/" + Optional.ofNullable(bucketId).orElse(businessNum) + "/" + url;
        }
    }

    @Override
    public String getCompleteVideoUrlFromPostAsset(SocialPostsAssets post, String businessNum) {
        return getCompleteMediaUrlFromParam(post.getVideoUrl(),post.getBucketId(),businessNum);
    }

    @Override
    public void flush() {
        socialPostsAssetsRepository.flush();
    }

    @Override
    public int updateBucketIdWhereBucketId(String sourceBucketId, String targetBucketId) {
        return socialPostsAssetsRepository.updateBucketIdWhereBucketId(sourceBucketId, targetBucketId);
    }

    @Override
    @Transactional
    public void saveAndFlush(SocialPostsAssets postAsset) {
        socialPostsAssetsRepository.saveAndFlush(postAsset);
    }

    @Override
    public List<SocialPostsAssets> findByIds(Set<Integer> ids) {
        return socialPostsAssetsRepository.findByIds(ids);
    }

    @Override
    public String getBaseCdnUrl() {
        return CacheManager.getInstance().getCache(SystemPropertiesCache.class).getCdnImageBaseUrl();
    }

    @Override
    public List<SocialPostsAssets> findAllByVideoUrl(List<String> socialAssets) {
        return socialPostsAssetsRepository.findAllByVideoUrl(socialAssets);
    }

    @Override
    public List<SocialPostsAssets> findAllByVideoUrlAndBucketId(List<String> socialAssets, String bucketId) {
        return socialPostsAssetsRepository.findAllByVideoUrlAndBucketId(socialAssets, bucketId);
    }

    @Override
    public String findThumbnailById(Integer id) {
        return socialPostsAssetsRepository.findThumbnailById(id);
    }

    @Override
    public SocialPostsAssets findByVideoUrl(String videoUrl) {
        return socialPostsAssetsRepository.findByVideoUrl(videoUrl);
    }

    @Override
    public SocialPostsAssets findByImageUrl(String mediaAssetUrl) {
        return socialPostsAssetsRepository.findByImageUrl(mediaAssetUrl);
    }

    @Override
    public SocialPostsAssets findByVideoUrlAndBucketId(String videoUrl, String bucketId) {
        return socialPostsAssetsRepository.findByVideoUrlAndBucketId(videoUrl, bucketId);
    }

    @Override
    public SocialPostsAssets findByImageUrlAndBucketId(String mediaAssetUrl, String bucketId) {
        return socialPostsAssetsRepository.findByImageUrlAndBucketId(mediaAssetUrl, bucketId);
    }

    @Override
    public List<SocialPostsAssets> findAllByMediaBucket(List<SocialPostsAssetsRepository.SocialPostMediaBucket> mediaBuckets, boolean isImage) {
        return socialPostsAssetsRepository.findByMediaBucket(mediaBuckets, isImage);
    }

    /**
     * for (MediaData mediaData : socialAssetsData) {
     *                 String originalMediaUrl = mediaData.getMediaUrl();
     *                 //remove domain(https://ddjkm7nmu27lx.cloudfront.net) from complete media url
     *                 if (originalMediaUrl != null) {
     *                     String processedUrl = originalMediaUrl.replaceFirst("^https://", "");
     *                     int firstSlashIndex = processedUrl.indexOf('/');
     *                     if (firstSlashIndex >= 0) {
     *                         mediaData.setMediaUrl(processedUrl.substring(firstSlashIndex + 1));
     *                     } else {
     *                         // Handle case where there's no slash after domain
     *                         mediaData.setMediaUrl("");
     *                     }
     * @param socialAssetsData
     * @param type
     * @param videoThumbnailUrl
     * @param businessId
     * @return
     */

    @Override
    public List<Integer> getAssetIdsForAIImages(List<MediaData> socialAssetsData, String type, String videoThumbnailUrl, Integer businessId) {
        if (CollectionUtils.isEmpty(socialAssetsData)) {
            return new ArrayList<>();
        }

        BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLite(businessId, false);
        List<Integer> assetIds = new ArrayList<>();

        try {
            for (MediaData mediaData : socialAssetsData) {
                List<MediaData> singleItemList = Collections.singletonList(mediaData);
                String originalMediaUrl = mediaData.getMediaUrl();
                //remove domain(https://ddjkm7nmu27lx.cloudfront.net) from complete media url
                if (originalMediaUrl != null) {
                    String processedUrl = originalMediaUrl.replaceFirst("^https://", "");
                    int firstSlashIndex = processedUrl.indexOf('/');
                    if (firstSlashIndex >= 0) {
                        mediaData.setMediaUrl(processedUrl.substring(firstSlashIndex + 1));
                    }

                    List<Integer> ids = isOldImagePath(singleItemList)
                            ? getAssetIdsV1(singleItemList, type, videoThumbnailUrl, businessId, businessLiteDTO)
                            : getAssetIdsV2(singleItemList, type, videoThumbnailUrl, businessId, businessLiteDTO);

                    if (CollectionUtils.isNotEmpty(ids)) {
                        assetIds.addAll(ids);
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("Error while parsing image path for businessId: {}", businessId, e);
        }

        return assetIds;
    }

    private boolean isOldImagePath(List<MediaData> socialAssetsData) throws Exception {
        if(CollectionUtils.isEmpty(socialAssetsData)) {
            throw new Exception("empty social asset data");
        }

        String mediaUrl = socialAssetsData.get(0).getMediaUrl();

        if(StringUtils.isEmpty(mediaUrl)){
            throw new Exception("empty media url");
        }

        String[] sp = mediaUrl.split("/");
        try {
            Long bucket = Long.parseLong(sp[0]);
            return false;
        } catch(Exception e) {
            return true;
        }
    }

    private List<Integer> getAssetIdsV1 (List <MediaData> socialAssetsData, String type, String videoThumbnailUrl, Integer businessId, BusinessLiteDTO businessLiteDTO) {
        List<String> socialAssets = extractMediaUrls(socialAssetsData);
        LOGGER.info("[getAssetIdsV1] : socialAssets {},type {} ", socialAssets, type);
        List<SocialPostsAssets> postAssets = fetchPostAssetsByType(socialAssets, type, String.valueOf(businessLiteDTO.getBusinessNumber()));
        Map<String, Integer> urlVsIdMap = new HashMap<>();

        if (CollectionUtils.isEmpty(postAssets)) {
            return handleNewAssets(socialAssetsData, type, videoThumbnailUrl, String.valueOf(businessLiteDTO.getBusinessNumber()), urlVsIdMap);
        } else {
            return handleExistingAssets(socialAssetsData, postAssets, type, videoThumbnailUrl, String.valueOf(businessLiteDTO.getBusinessNumber()), urlVsIdMap);
        }
    }

    private List<String> extractMediaUrls(List<MediaData> mediaDataList) {
        return mediaDataList.stream()
                .map(MediaData::getMediaUrl)
                .collect(Collectors.toList());
    }

    private List<SocialPostsAssets> fetchPostAssetsByType(List<String> socialAssets, String type, String bucketId) {
        switch (type) {
            case IMAGE:
                return findAllByImageUrlAndBucketId(socialAssets, bucketId);
            case VIDEO:
                return findAllByVideoUrlAndBucketId(socialAssets, bucketId);
            default:
                return Collections.emptyList();
        }
    }

    private List<Integer> getAssetIdsV2 (List < MediaData > socialAssetsData, String type, String videoThumbnailUrl, Integer businessId, BusinessLiteDTO businessLiteDTO) {
        List<String> socialAssets = socialAssetsData.stream().map(MediaData::getMediaUrl).collect(Collectors.toList());
        LOGGER.info("getIds : socialAssets {},type {} ", socialAssets, type);
        List<Integer> ids = new ArrayList<>();
        List<MediaBucket> mediaBucketList = socialAssetsData.stream().map(s->splitMediaPath(s)).collect(Collectors.toList());
        List<SocialPostsAssetsRepository.SocialPostMediaBucket> postAssetBucket = getSocialPostAssetBucket(mediaBucketList);
        List<SocialPostsAssets> postAssets = findAllByMediaBucket(postAssetBucket, true);;

        List<SocialPostsAssets> addedAssets = new ArrayList<>();
        Map<String, Integer> urlVsIdMap = new HashMap<>();
        if (CollectionUtils.isEmpty(postAssets)) {
            addedAssets = addAssetsV2(mediaBucketList, type, videoThumbnailUrl, businessLiteDTO.getBusinessNumber().toString());
        } else {
            List<MediaBucket> existingImageOrVideos = new ArrayList<>();
            List<SocialPostsAssets> postToUpdate = new ArrayList<>();
            for (SocialPostsAssets postAsset : postAssets) {
                if (IMAGE.equals(type)) {
                    existingImageOrVideos.add(new MediaBucket(postAsset.getImageUrl(), postAsset.getBucketId(),postAsset.getAssetMetaData()));
                    urlVsIdMap.put(postAsset.getImageUrl(), postAsset.getId());
                } else {
                    existingImageOrVideos.add(new MediaBucket(postAsset.getVideoUrl(), postAsset.getBucketId(),postAsset.getAssetMetaData()));
                    postAsset.setVideoThumbnail(videoThumbnailUrl);
                    postToUpdate.add(postAsset);
                    urlVsIdMap.put(postAsset.getVideoUrl(), postAsset.getId());
                }
            }
            if (CollectionUtils.isNotEmpty(postToUpdate)) {
                save(postToUpdate);
            }
            List<MediaBucket> remainingData = new ArrayList<>();
            List<String> existingAssets = existingImageOrVideos.stream().map(MediaBucket::getMediaUrl).collect(Collectors.toList());
            LOGGER.info("socialAssetsData: {}  existingImageOrVideos {}", socialAssetsData, existingImageOrVideos);
            for (MediaBucket media : mediaBucketList) {
                if (!existingAssets.contains(media.getMediaUrl())) {
                    remainingData.add(media);
                }
            }
            //socialAssetsData.removeAll(existingImageOrVideos);
            if (CollectionUtils.isNotEmpty(remainingData)) {
                addedAssets = addAssetsV2(remainingData, type, videoThumbnailUrl, businessLiteDTO.getBusinessNumber().toString());
            }
        }
        for (SocialPostsAssets postAsset : addedAssets) {
            if (IMAGE.equals(type)) {
                urlVsIdMap.put(postAsset.getImageUrl(), postAsset.getId());
            } else if((VIDEO.equals(type))){
                urlVsIdMap.put(postAsset.getVideoUrl(), postAsset.getId());
            }
        }

        for(MediaBucket mediaBucket: mediaBucketList) {
            if(urlVsIdMap.containsKey(mediaBucket.getMediaUrl())) {
                ids.add(urlVsIdMap.get(mediaBucket.getMediaUrl()));
            }
        }
        return ids;
    }

    private MediaBucket splitMediaPath(MediaData mediaData) {
        String completeUrl = mediaData.getMediaUrl();
        String[] sp = completeUrl.split("/");
        String url = "";
        for(int i=1; i<sp.length; i++) {
            url += sp[i];
            if(i < sp.length-1) {
                url += "/";
            }
        }

        return new MediaBucket(url, sp[0], mediaData.getMediaMetaData());
    }

    private List<SocialPostsAssets> addAssetsV2 (List < MediaBucket > socialAssets, String type, String videoThumbnailUrl, String entepriseNumber){
        List<SocialPostsAssets> addedAssets = new ArrayList<>();
        SocialPostsAssets asset;
        for (MediaBucket mediaData : socialAssets) {
            String url = mediaData.getMediaUrl();
            String assetMetaData = mediaData.getMediaMetaData();
            asset = new SocialPostsAssets();
            if (IMAGE.equals(type)) {
                asset.setImageUrl(url);
            } else {
                asset.setVideoUrl(url);
                if (com.birdeye.social.utils.StringUtils.isNotEmpty(videoThumbnailUrl))
                    asset.setVideoThumbnail(videoThumbnailUrl);
            }
            asset.setAssetMetaData(assetMetaData);
            asset.setBucketId(mediaData.getBucketId());
            asset.setCreatedDate(Calendar.getInstance(TimeZone.getTimeZone("UTC")).getTime());
            save(asset);
            addedAssets.add(asset);
        }
        flush();
        return addedAssets;
    }

    private List<Integer> handleNewAssets(List<MediaData> socialAssetsData, String type, String videoThumbnailUrl, String bucketId, Map<String, Integer> urlVsIdMap) {
        List<SocialPostsAssets> addedAssets = addAssets(socialAssetsData, type, videoThumbnailUrl, bucketId);
        mapAssetsToIds(addedAssets, type, urlVsIdMap);
        return buildIdListFromMap(socialAssetsData, urlVsIdMap);
    }

    private List<SocialPostsAssets> addAssets (List < MediaData > socialAssets, String type, String videoThumbnailUrl, String entepriseNumber){
        List<SocialPostsAssets> addedAssets = new ArrayList<>();
        SocialPostsAssets asset;
        for (MediaData mediaData : socialAssets) {
            String url = mediaData.getMediaUrl();
            String assetMetaData = mediaData.getMediaMetaData();
            asset = new SocialPostsAssets();
            if (IMAGE.equals(type)) {
                asset.setImageUrl(url);
            } else {
                asset.setVideoUrl(url);
                if (com.birdeye.social.utils.StringUtils.isNotEmpty(videoThumbnailUrl))
                    asset.setVideoThumbnail(videoThumbnailUrl);
            }
            asset.setAssetMetaData(assetMetaData);
            asset.setBucketId(entepriseNumber);
            asset.setCreatedDate(Calendar.getInstance(TimeZone.getTimeZone("UTC")).getTime());
            save(asset);
            addedAssets.add(asset);
        }
        flush();
        return addedAssets;
    }

    private void mapAssetsToIds(List<SocialPostsAssets> assets, String type, Map<String, Integer> urlVsIdMap) {
        for (SocialPostsAssets asset : assets) {
            if (IMAGE.equals(type)) {
                urlVsIdMap.put(asset.getImageUrl(), asset.getId());
            } else if (VIDEO.equals(type)) {
                urlVsIdMap.put(asset.getVideoUrl(), asset.getId());
            }
        }
    }

    private List<Integer> buildIdListFromMap(List<MediaData> socialAssetsData, Map<String, Integer> urlVsIdMap) {
        return socialAssetsData.stream()
                .map(MediaData::getMediaUrl)
                .filter(urlVsIdMap::containsKey)
                .map(urlVsIdMap::get)
                .collect(Collectors.toList());
    }

    private List<SocialPostsAssetsRepository.SocialPostMediaBucket> getSocialPostAssetBucket(List<MediaBucket> mediaBucketList) {
        if(CollectionUtils.isEmpty(mediaBucketList)) return new ArrayList<>();
        return mediaBucketList.stream()
                .map(s -> new SocialPostsAssetsRepository.SocialPostMediaBucket(s.getMediaUrl(), s.getBucketId()))
                .collect(Collectors.toList());
    }

    private List<Integer> handleExistingAssets(List<MediaData> socialAssetsData, List<SocialPostsAssets> postAssets, String type, String videoThumbnailUrl, String bucketId, Map<String, Integer> urlVsIdMap) {
        List<MediaData> existingMediaData = new ArrayList<>();
        List<SocialPostsAssets> postToUpdate = new ArrayList<>();

        mapExistingAssets(postAssets, type, videoThumbnailUrl, existingMediaData, postToUpdate, urlVsIdMap);
        if (CollectionUtils.isNotEmpty(postToUpdate)) {
            save(postToUpdate);
        }

        List<MediaData> remainingData = filterNewAssets(socialAssetsData, existingMediaData);
        if (CollectionUtils.isNotEmpty(remainingData)) {
            List<SocialPostsAssets> addedAssets = addAssets(remainingData, type, videoThumbnailUrl, bucketId);
            mapAssetsToIds(addedAssets, type, urlVsIdMap);
        }
        return buildIdListFromMap(socialAssetsData, urlVsIdMap);
    }

    private void mapExistingAssets(List<SocialPostsAssets> postAssets, String type, String videoThumbnailUrl, List<MediaData> existingMediaData, List<SocialPostsAssets> postToUpdate, Map<String, Integer> urlVsIdMap) {
        for (SocialPostsAssets postAsset : postAssets) {
            if (IMAGE.equals(type)) {
                existingMediaData.add(new MediaData(postAsset.getImageUrl(), postAsset.getAssetMetaData()));
                urlVsIdMap.put(postAsset.getImageUrl(), postAsset.getId());
            } else if (VIDEO.equals(type)) {
                existingMediaData.add(new MediaData(postAsset.getVideoUrl(), postAsset.getAssetMetaData()));
                postAsset.setVideoThumbnail(videoThumbnailUrl);
                postToUpdate.add(postAsset);
                urlVsIdMap.put(postAsset.getVideoUrl(), postAsset.getId());
            }
        }
    }

    private List<MediaData> filterNewAssets(List<MediaData> allMediaData, List<MediaData> existingMediaData) {
        Set<String> existingUrls = existingMediaData.stream()
                .map(MediaData::getMediaUrl)
                .collect(Collectors.toSet());
        return allMediaData.stream()
                .filter(media -> !existingUrls.contains(media.getMediaUrl()))
                .collect(Collectors.toList());
    }
}
