package com.birdeye.social.service;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.dao.SocialPostRepository;
import com.birdeye.social.dao.SocialPostScheduleInfoRepo;
import com.birdeye.social.dto.BusinessLiteDTO;
import com.birdeye.social.entities.LocationMovementPostDTO;
import com.birdeye.social.entities.SocialPost;
import com.birdeye.social.entities.SocialPostScheduleInfo;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({SocialPostServiceImpl.class})
public class SocialPostServiceImplTest {

    @Mock
    private IBusinessCoreService businessCoreService;

    @Mock
    private ISocialPostsAssetService socialPostsAssetService;

    @Mock
    private SocialPostScheduleInfoRepo socialPostScheduleInfoRepository;

    @Mock
    private SocialPostRepository socialPostRepository;

    @InjectMocks
    private SocialPostServiceImpl socialPostService;

    private LocationMovementPostDTO locationMovementPostDTO;
    private BusinessLiteDTO businessLiteDTO;
    private List<SocialPostScheduleInfo> socialPostScheduleInfoList;
    private List<SocialPost> socialPostList;

    @Before
    public void setUp() {
        // Setup test data
        locationMovementPostDTO = LocationMovementPostDTO.builder()
                .sourceBusinessId(100)
                .sourceEnterpriseId(200)
                .targetEnterpriseId(300)
                .channelAndPageIdMap(createChannelAndPageIdMap())
                .build();

        businessLiteDTO = new BusinessLiteDTO();
        businessLiteDTO.setBusinessId(300);
        businessLiteDTO.setBusinessNumber(123456L);

        socialPostScheduleInfoList = createSocialPostScheduleInfoList();
        socialPostList = createSocialPostList();
    }

    @Test
    public void testUpdateSocialPostScheduleInfo_Success() {
        // Arrange
        when(businessCoreService.getBusinessLite(300, false)).thenReturn(businessLiteDTO);
        when(socialPostScheduleInfoRepository.findByEnterpriseIdAndIsPublished(200, 0))
                .thenReturn(socialPostScheduleInfoList);
        when(socialPostRepository.findByIdIn(anyList())).thenReturn(socialPostList);
        when(socialPostsAssetService.updateBucketIdWhereBucketId("200", "300")).thenReturn(5);

        // Act
        socialPostService.updateSocialPostScheduleInfo(locationMovementPostDTO);

        // Assert
        verify(businessCoreService, times(1)).getBusinessLite(300, false);
        verify(socialPostsAssetService, times(1)).updateBucketIdWhereBucketId("200", "300");
        verify(socialPostScheduleInfoRepository, times(1)).findByEnterpriseIdAndIsPublished(200, 0);
        verify(socialPostRepository, times(1)).findByIdIn(anyList());
    }

    @Test
    public void testUpdateSocialPostScheduleInfo_BusinessCoreServiceFailure() {
        // Arrange
        when(businessCoreService.getBusinessLite(300, false))
                .thenThrow(new RuntimeException("Business not found"));

        // Act & Assert
        try {
            socialPostService.updateSocialPostScheduleInfo(locationMovementPostDTO);
            fail("Expected BirdeyeSocialException to be thrown");
        } catch (BirdeyeSocialException e) {
            assertEquals(ErrorCodes.BUSINESS_NOT_FOUND, e.getErrorCode());
            assertTrue(e.getMessage().contains("Failed to retrieve business details for target enterprise"));
        }

        verify(businessCoreService, times(1)).getBusinessLite(300, false);
        verify(socialPostsAssetService, never()).updateBucketIdWhereBucketId(anyString(), anyString());
    }

    @Test
    public void testUpdateSocialPostScheduleInfo_NoPostsFound() {
        // Arrange
        when(businessCoreService.getBusinessLite(300, false)).thenReturn(businessLiteDTO);
        when(socialPostScheduleInfoRepository.findByEnterpriseIdAndIsPublished(200, 0))
                .thenReturn(Collections.emptyList());

        // Act
        socialPostService.updateSocialPostScheduleInfo(locationMovementPostDTO);

        // Assert
        verify(businessCoreService, times(1)).getBusinessLite(300, false);
        verify(socialPostsAssetService, times(1)).updateBucketIdWhereBucketId("200", "300");
        verify(socialPostScheduleInfoRepository, times(1)).findByEnterpriseIdAndIsPublished(200, 0);
        verify(socialPostRepository, never()).findByIdIn(anyList());
    }

    @Test
    public void testUpdateSocialPostScheduleInfo_AssetUpdateFailure() {
        // Arrange
        when(businessCoreService.getBusinessLite(300, false)).thenReturn(businessLiteDTO);
        when(socialPostScheduleInfoRepository.findByEnterpriseIdAndIsPublished(200, 0))
                .thenReturn(socialPostScheduleInfoList);
        when(socialPostRepository.findByIdIn(anyList())).thenReturn(socialPostList);
        when(socialPostsAssetService.updateBucketIdWhereBucketId("200", "300"))
                .thenThrow(new RuntimeException("Database error"));

        // Act - Should not throw exception, should continue with schedule info update
        socialPostService.updateSocialPostScheduleInfo(locationMovementPostDTO);

        // Assert
        verify(businessCoreService, times(1)).getBusinessLite(300, false);
        verify(socialPostsAssetService, times(1)).updateBucketIdWhereBucketId("200", "300");
        verify(socialPostScheduleInfoRepository, times(1)).findByEnterpriseIdAndIsPublished(200, 0);
        verify(socialPostRepository, times(1)).findByIdIn(anyList());
    }

    @Test
    public void testUpdateSocialPostScheduleInfo_EmptyChannelAndPageIdMap() {
        // Arrange
        locationMovementPostDTO.setChannelAndPageIdMap(Collections.emptyMap());
        when(businessCoreService.getBusinessLite(300, false)).thenReturn(businessLiteDTO);
        when(socialPostScheduleInfoRepository.findByEnterpriseIdAndIsPublished(200, 0))
                .thenReturn(socialPostScheduleInfoList);

        // Act
        socialPostService.updateSocialPostScheduleInfo(locationMovementPostDTO);

        // Assert
        verify(businessCoreService, times(1)).getBusinessLite(300, false);
        verify(socialPostsAssetService, times(1)).updateBucketIdWhereBucketId("200", "300");
        verify(socialPostScheduleInfoRepository, times(1)).findByEnterpriseIdAndIsPublished(200, 0);
        verify(socialPostRepository, never()).findByIdIn(anyList());
    }

    // Helper methods
    private Map<Integer, List<String>> createChannelAndPageIdMap() {
        Map<Integer, List<String>> channelAndPageIdMap = new HashMap<>();
        channelAndPageIdMap.put(110, Arrays.asList("page1", "page2")); // Facebook
        channelAndPageIdMap.put(120, Arrays.asList("page3")); // Twitter
        return channelAndPageIdMap;
    }

    private List<SocialPostScheduleInfo> createSocialPostScheduleInfoList() {
        List<SocialPostScheduleInfo> list = new ArrayList<>();
        
        SocialPostScheduleInfo scheduleInfo1 = SocialPostScheduleInfo.builder()
                .id(1)
                .socialPostId(101)
                .enterpriseId(200)
                .sourceId(110)
                .isPublished(0)
                .build();
        
        SocialPostScheduleInfo scheduleInfo2 = SocialPostScheduleInfo.builder()
                .id(2)
                .socialPostId(102)
                .enterpriseId(200)
                .sourceId(120)
                .isPublished(0)
                .build();
        
        list.add(scheduleInfo1);
        list.add(scheduleInfo2);
        return list;
    }

    private List<SocialPost> createSocialPostList() {
        List<SocialPost> list = new ArrayList<>();
        
        SocialPost socialPost1 = new SocialPost();
        socialPost1.setId(101);
        socialPost1.setApprovalWorkflowId(null); // No approval workflow
        
        SocialPost socialPost2 = new SocialPost();
        socialPost2.setId(102);
        socialPost2.setApprovalWorkflowId(null); // No approval workflow
        
        list.add(socialPost1);
        list.add(socialPost2);
        return list;
    }
}
